"use client";

import React, { Component, ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, FileX } from "lucide-react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onRetry?: () => void;
  allowContinue?: boolean;
  onContinue?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: string | null;
}

export class CertificatePreviewErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: error.stack || null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Certificate Preview Error Boundary caught an error:", error);
    console.error("Error Info:", errorInfo);
    
    this.setState({
      hasError: true,
      error,
      errorInfo: errorInfo.componentStack || null,
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleContinue = () => {
    if (this.props.onContinue) {
      this.props.onContinue();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Erreur de Prévisualisation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-red-700">
              <p className="font-medium mb-2">
                Une erreur est survenue lors du chargement de la prévisualisation :
              </p>
              <p className="bg-red-100 p-3 rounded border border-red-200 font-mono text-xs">
                {this.state.error?.message || "Erreur inconnue"}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={this.handleRetry}
                variant="outline"
                className="flex items-center gap-2 border-red-300 text-red-700 hover:bg-red-100"
              >
                <RefreshCw className="h-4 w-4" />
                Réessayer la prévisualisation
              </Button>

              {this.props.allowContinue && (
                <Button
                  onClick={this.handleContinue}
                  variant="default"
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <FileX className="h-4 w-4" />
                  Continuer sans prévisualisation
                </Button>
              )}
            </div>

            <div className="text-xs text-red-600 bg-red-100 p-3 rounded border border-red-200">
              <p className="font-medium mb-1">Note :</p>
              <p>
                Cette erreur n'affecte pas la validité du certificat. Vous pouvez continuer 
                le processus de livraison même sans prévisualisation.
              </p>
            </div>

            {process.env.NODE_ENV === "development" && this.state.errorInfo && (
              <details className="text-xs text-gray-600">
                <summary className="cursor-pointer font-medium">
                  Détails techniques (développement)
                </summary>
                <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto">
                  {this.state.errorInfo}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}
