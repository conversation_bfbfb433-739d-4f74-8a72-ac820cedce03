"use client";

import { Certificate } from "@/actions/types";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useCertificatePreview } from "@/hooks/use-certificate-preview";
import { 
  AlertCircle, 
  FileText, 
  Loader2, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  FileX,
  Clock
} from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { CertificatePreviewErrorBoundary } from "./certificate-preview-error-boundary";

interface CertificatePreviewProps {
  certificate: Certificate;
  isDelivered?: boolean;
  className?: string;
  allowContinueWithoutPreview?: boolean;
  onContinueWithoutPreview?: () => void;
}

export function CertificatePreviewEnhanced({
  certificate,
  isDelivered = false,
  className = "",
  allowContinueWithoutPreview = false,
  onContinueWithoutPreview,
}: CertificatePreviewProps) {
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [hasFailedPermanently, setHasFailedPermanently] = useState(false);

  const {
    generatePreview,
    cleanupPreview,
    clearCache,
    cancelGeneration,
    resetState,
    isGenerating,
    error: previewError,
    retryCount,
    progress,
    maxRetries,
    clearError,
  } = useCertificatePreview();

  // Memoize certificate key for efficient re-renders
  const certificateKey = useMemo(
    () => `${certificate.$id}-${certificate.status}-${certificate.updatedAt}`,
    [certificate.$id, certificate.status, certificate.updatedAt]
  );

  // Enhanced load preview function
  const loadPreview = useCallback(
    async (forceRefresh = false) => {
      if (!certificate.$id) {
        console.warn("No certificate ID provided");
        return;
      }

      try {
        clearError();
        setHasFailedPermanently(false);

        console.log(`Loading preview for certificate ${certificate.$id}`);

        const url = await generatePreview(
          certificate.$id,
          certificate.status,
          forceRefresh
        );

        if (url) {
          // Clean up old URL if it exists
          if (previewUrl && previewUrl !== url) {
            cleanupPreview(previewUrl);
          }
          setPreviewUrl(url);
          console.log("Preview loaded successfully");
        } else if (!isGenerating) {
          throw new Error("No preview URL returned from server");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        console.error("Error loading preview:", errorMessage);

        // Check if this is a permanent failure
        if (errorMessage.includes(`Échec après ${maxRetries} tentatives`)) {
          setHasFailedPermanently(true);
          toast({
            title: "Preview Generation Failed",
            description: `Failed to generate preview after ${maxRetries} attempts. You can continue without preview.`,
            variant: "destructive",
          });
        }
      }
    },
    [
      certificate.$id,
      certificate.status,
      generatePreview,
      cleanupPreview,
      clearError,
      isGenerating,
      previewUrl,
      maxRetries,
      toast,
    ]
  );

  // Load preview when certificate changes
  useEffect(() => {
    let mounted = true;

    const initPreview = async () => {
      if (mounted) {
        await loadPreview();
      }
    };

    initPreview();

    return () => {
      mounted = false;
      cancelGeneration();
    };
  }, [certificateKey, loadPreview, cancelGeneration]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        cleanupPreview(previewUrl);
      }
      cancelGeneration();
    };
  }, [previewUrl, cleanupPreview, cancelGeneration]);

  // Enhanced refresh handler
  const handleRefresh = useCallback(() => {
    resetState();
    setHasFailedPermanently(false);
    clearCache(certificate.$id);
    loadPreview(true);
  }, [certificate.$id, clearCache, loadPreview, resetState]);

  // Handle continue without preview
  const handleContinueWithoutPreview = useCallback(() => {
    if (onContinueWithoutPreview) {
      onContinueWithoutPreview();
    }
  }, [onContinueWithoutPreview]);

  // Determine display state
  const displayError = previewError;
  const showLoading = isGenerating && !previewUrl;
  const showError = (displayError || hasFailedPermanently) && !isGenerating && !previewUrl;
  const showPreview = previewUrl && !showLoading && !showError;

  return (
    <CertificatePreviewErrorBoundary
      onRetry={handleRefresh}
      allowContinue={allowContinueWithoutPreview}
      onContinue={handleContinueWithoutPreview}
    >
      <Card className={`${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {isDelivered ? "Certificate Delivered" : "Certificate Preview"}
          </CardTitle>

          <div className="flex items-center gap-2">
            {/* Status indicator */}
            {showLoading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>
                  {progress || (retryCount > 0 
                    ? `Attempt ${retryCount + 1}/${maxRetries}...` 
                    : "Loading...")}
                </span>
              </div>
            )}

            {showPreview && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Preview Ready</span>
              </div>
            )}

            {showError && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <XCircle className="h-4 w-4" />
                <span>Preview Failed</span>
              </div>
            )}

            {/* Action buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isGenerating}
            >
              <RefreshCw className={`h-4 w-4 ${isGenerating ? "animate-spin" : ""}`} />
              Refresh
            </Button>

            {allowContinueWithoutPreview && showError && (
              <Button
                variant="default"
                size="sm"
                onClick={handleContinueWithoutPreview}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <FileX className="h-4 w-4 mr-2" />
                Continue Without Preview
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="min-h-[700px] bg-muted/30 rounded-lg p-0">
          {showPreview && (
            <iframe
              src={previewUrl}
              className="w-full h-[700px] rounded-lg"
              style={{
                border: "none",
                backgroundColor: "white",
              }}
              title="Certificate Preview"
              loading="lazy"
            />
          )}

          {showLoading && (
            <div className="flex flex-col items-center justify-center h-[700px] gap-4">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
              <div className="text-center space-y-2">
                <p className="text-lg font-medium">
                  {progress || (retryCount > 0
                    ? `Retry ${retryCount + 1}/${maxRetries}`
                    : "Generating Preview...")}
                </p>
                <p className="text-sm text-muted-foreground">
                  This may take a few seconds
                </p>
                {retryCount > 0 && (
                  <div className="flex items-center gap-2 text-xs text-orange-600">
                    <Clock className="h-3 w-3" />
                    <span>Automatic retry in progress</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {showError && (
            <div className="flex flex-col items-center justify-center h-[700px] gap-4">
              <AlertCircle className="h-12 w-12 text-destructive" />
              <div className="text-center space-y-4 max-w-md">
                <p className="text-lg font-medium text-destructive">
                  Preview Generation Failed
                </p>
                <p className="text-sm text-muted-foreground">
                  {displayError || "Unable to generate certificate preview"}
                </p>
                
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={handleRefresh}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </Button>

                  {allowContinueWithoutPreview && (
                    <Button
                      onClick={handleContinueWithoutPreview}
                      variant="default"
                      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                    >
                      <FileX className="h-4 w-4" />
                      Continue Without Preview
                    </Button>
                  )}
                </div>

                <div className="text-xs text-muted-foreground bg-muted p-3 rounded">
                  <p className="font-medium mb-1">Note:</p>
                  <p>
                    This error does not affect the certificate validity. 
                    You can continue the delivery process without preview.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </CertificatePreviewErrorBoundary>
  );
}
