"use server";

import { createAdminClient } from "@/lib/server/appwrite";
import {
  AGENTS_COLLECTION_ID,
  CERTIFICATES_COLLECTION_ID,
  CHEFS_COLLECTION_ID,
  CITIZENS_COLLECTION_ID,
  DATABASE_ID,
  DOCUMENTS_COLLECTION_ID,
  QUARTIERS_COLLECTION_ID,
} from "@/lib/server/database";
import { PdfGenerator } from "@/lib/services/pdf-generator";
import { Query } from "node-appwrite";
import { CERTIFICATE_STATUS } from "./auth/constants";
import { getCurrentUser } from "./auth/session";
import { Certificate } from "./types";

export async function updateDocumentStatus(
  documentId: string,
  status: "approved" | "rejected"
) {
  try {
    const { databases } = await createAdminClient();
    const response = await databases.updateDocument(
      DATABASE_ID,
      DOCUMENTS_COLLECTION_ID,
      documentId,
      {
        status: status,
        // updatedAt: new Date().toISOString(),
      }
    );
    return response;
  } catch (error) {
    console.error(
      "Erreur lors de la mise à jour du statut du document:",
      error
    );
    throw error;
  }
}

export async function updateCertificateStatus(
  certificateId: string,
  {
    status,
    verifiedBy,
    verifiedAt,
    verificationNotes,
  }: {
    status: string;
    verifiedBy: string;
    verifiedAt: string;
    verificationNotes: string;
  }
) {
  try {
    const { databases } = await createAdminClient();
    const response = await databases.updateDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId,
      {
        status: status,
        updatedAt: new Date().toISOString(),
        verifiedBy,
        verifiedAt,
        verificationNotes,
      }
    );
    return response;
  } catch (error) {
    console.error(
      "Erreur lors de la mise à jour du statut du certificat:",
      error
    );
    throw error;
  }
}

export async function downloadCertificate(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    );

    // Vérification des permissions
    const isAuthorized =
      user.prefs?.role === "admin" ||
      certificate.citizenId === user.$id ||
      certificate.chefId === user.$id ||
      certificate.agentId === user.$id;

    if (!isAuthorized) {
      throw new Error("Vous n'avez pas les permissions nécessaires");
    }

    // Vérification si l'utilisateur a déjà téléchargé le certificat
    const hasDownloaded = certificate.downloads?.includes(user.$id);
    if (hasDownloaded && user.prefs?.role !== "admin") {
      throw new Error("Vous avez déjà téléchargé ce certificat");
    }

    // Vérification de l'URL du document
    if (!certificate.documentUrl) {
      throw new Error("Le document PDF n'est pas disponible");
    }

    // Mise à jour des informations de téléchargement
    const downloads = certificate.downloads || [];
    if (!downloads.includes(user.$id)) {
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        certificateId,
        {
          downloads: [...downloads, user.$id],
          downloadedAt: certificate.downloadedAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
    }

    // Retourner l'URL du document pour le téléchargement côté client
    return {
      success: true,
      documentUrl: certificate.documentUrl,
    };
  } catch (error) {
    console.error("Erreur lors du téléchargement du certificat:", error);
    throw error;
  }
}

export async function getRecentCertificates() {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();
    const { documents } = (await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      [Query.orderDesc("$createdAt"), Query.limit(3)]
    )) as { documents: Certificate[] };

    // Récupérer tous les IDs uniques
    const citizenIds = [...new Set(documents.map((doc) => doc.citizenId))];
    const agentIds = Array.from(
      new Set(
        documents
          .map((doc) => doc.agentId)
          .filter((id): id is string => id !== null && id !== undefined)
      )
    );

    // Récupérer les données en parallèle
    const [citizensData, agentsData] = await Promise.all([
      // Récupération des citoyens
      databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
        Query.equal("userId", citizenIds),
      ]),
      // Récupération des agents
      agentIds.length > 0
        ? databases.listDocuments(DATABASE_ID, AGENTS_COLLECTION_ID, [
            Query.equal("$id", agentIds),
          ])
        : Promise.resolve({ documents: [] }),
    ]);

    // Créer des Maps pour un accès rapide
    const citizensMap = new Map(
      citizensData.documents.map((citizen) => [citizen.userId, citizen])
    );
    const agentsMap = new Map(
      agentsData.documents.map((agent) => [agent.$id, agent])
    );

    // Enrichir les certificats avec les données associées
    const enrichedCertificates = documents.map((doc) => {
      const citizen = citizensMap.get(doc.citizenId);
      const agent = doc.agentId ? agentsMap.get(doc.agentId) : null;

      return {
        ...doc,
        citizenName: citizen ? `${citizen.prenom} ${citizen.nom}` : "N/A",
        agentName: agent ? `${agent.prenom} ${agent.nom}` : null,
      };
    });

    return {
      success: true,
      certificates: enrichedCertificates,
    };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des certificats récents:",
      error
    );
    throw error;
  }
}

export async function getAllCertificates(params: {
  limit?: number;
  offset?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  status?: CERTIFICATE_STATUS;
}) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();
    const queries: string[] = [];

    // Filtres selon le rôle
    switch (user.prefs?.role) {
      case "admin":
        // Pas de filtre, accès à tout
        break;
      case "chef":
        // Uniquement les certificats de son quartier
        queries.push(Query.equal("chefId", user.$id));
        break;
      case "agent":
        // Uniquement les certificats assignés
        queries.push(Query.equal("agentId", user.$id));
        break;
      case "citizen":
        // Uniquement ses certificats
        queries.push(Query.equal("citizenId", user.$id));
        break;
      default:
        throw new Error("Rôle non autorisé");
    }

    // Ajout de la recherche si présente
    if (params.search) {
      queries.push(Query.search("reference", params.search));
    }

    // Ajout du filtre de statut
    if (params.status) {
      queries.push(Query.equal("status", params.status));
    }

    // Ajout du tri
    if (params.sortBy) {
      if (params.sortOrder === "asc") {
        queries.push(Query.orderAsc(params.sortBy));
      } else {
        queries.push(Query.orderDesc(params.sortBy));
      }
    } else {
      // Tri par défaut sur la date de création
      queries.push(Query.orderDesc("$createdAt"));
    }

    // Ajout de la pagination
    if (params.limit) {
      queries.push(Query.limit(params.limit));
    }
    if (params.offset) {
      queries.push(Query.offset(params.offset));
    }

    // Récupération des certificats
    const { documents, total } = await databases.listDocuments(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      queries
    );

    // Récupérer tous les IDs uniques
    const citizenIds = [...new Set(documents.map((doc) => doc.citizenId))];
    const agentIds = [
      ...new Set(documents.map((doc) => doc.agentId).filter(Boolean)),
    ];
    const chefIds = [...new Set(documents.map((doc) => doc.chefId))];

    // Récupérer les données en parallèle
    const [citizensData, agentsData, quartiersData] = await Promise.all([
      // Récupération des citoyens
      citizenIds.length > 0
        ? databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
            Query.equal("userId", citizenIds),
          ])
        : Promise.resolve({ documents: [] }),
      // Récupération des agents
      agentIds.length > 0
        ? databases.listDocuments(DATABASE_ID, AGENTS_COLLECTION_ID, [
            Query.equal("$id", agentIds),
          ])
        : Promise.resolve({ documents: [] }),
      // Récupération des quartiers
      chefIds.length > 0
        ? databases.listDocuments(DATABASE_ID, QUARTIERS_COLLECTION_ID, [
            Query.equal("chefId", chefIds),
          ])
        : Promise.resolve({ documents: [] }),
    ]);

    // Créer des Maps pour un accès rapide
    const citizensMap = new Map(
      citizensData.documents.map((citizen) => [citizen.userId, citizen])
    );
    const agentsMap = new Map(
      agentsData.documents.map((agent) => [agent.$id, agent])
    );
    const quartiersMap = new Map(
      quartiersData.documents.map((quartier) => [quartier.chefId, quartier])
    );

    // Enrichir les certificats
    const enrichedCertificates = documents.map((doc) => {
      const citizen = citizensMap.get(doc.citizenId);
      const agent = doc.agentId ? agentsMap.get(doc.agentId) : null;
      const quartier = quartiersMap.get(doc.chefId);

      return {
        ...doc,
        citizenName: citizen ? `${citizen.prenom} ${citizen.nom}` : "N/A",
        agentName: agent ? `${agent.prenom} ${agent.nom}` : null,
        quartierInfo: quartier
          ? `${quartier.nom} (${quartier.commune})`
          : "N/A",
      };
    });

    return {
      success: true,
      certificates: enrichedCertificates,
      pagination: {
        total,
        limit: params.limit || 10,
        offset: params.offset || 0,
      },
    };
  } catch (error) {
    console.error("Erreur lors de la récupération des certificats:", error);
    throw error;
  }
}

export async function getCertificateWithFullData(certificateId: string) {
  try {
    const { user } = await getCurrentUser();
    if (!user) throw new Error("Utilisateur non authentifié");

    const { databases } = await createAdminClient();

    // Récupération du certificat
    const certificate = (await databases.getDocument(
      DATABASE_ID,
      CERTIFICATES_COLLECTION_ID,
      certificateId
    )) as Certificate;

    // Récupération des données associées en parallèle
    const [citizenData, quartierData, chefData] = await Promise.all([
      // Informations du citoyen
      databases.listDocuments(DATABASE_ID, CITIZENS_COLLECTION_ID, [
        Query.equal("userId", certificate.citizenId),
        Query.limit(1),
      ]),
      // Informations du quartier
      databases.listDocuments(DATABASE_ID, QUARTIERS_COLLECTION_ID, [
        Query.equal("chefId", certificate.chefId),
        Query.limit(1),
      ]),
      // Informations du chef du quartier
      databases.listDocuments(DATABASE_ID, CHEFS_COLLECTION_ID, [
        Query.equal("userId", certificate.chefId),
        Query.limit(1),
      ]),
    ]);

    const citizen = citizenData.documents[0];
    const quartier = quartierData.documents[0];
    const chef = chefData.documents[0];

    if (!citizen) {
      throw new Error("Informations du citoyen non trouvées");
    }

    if (!quartier) {
      throw new Error("Informations du quartier non trouvées");
    }

    if (!chef) {
      throw new Error("Informations du chef du quartier non trouvées");
    }

    // Vérification des permissions selon le rôle
    const userRole = user.prefs?.role;
    const hasAccess =
      userRole === "admin" || // Admin a accès à tout
      (userRole === "chef" && certificate.chefId === user.$id) || // Chef de quartier uniquement à ses certificats
      (userRole === "agent" && certificate.agentId === user.$id) || // Agent uniquement aux certificats assignés
      (userRole === "citizen" && certificate.citizenId === user.$id); // Citoyen uniquement à ses certificats

    if (!hasAccess) {
      throw new Error("Non autorisé à accéder à ce certificat");
    }

    return {
      success: true,
      certificate: {
        ...certificate,
        citizen,
        quartier,
        chef,
        citizenName: `${citizen.prenom} ${citizen.nom}`,
        chefQuartierName: `${chef.prenom} ${chef.nom}`,
      },
    };
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des données du certificat:",
      error
    );
    throw error;
  }
}

export async function getCertificatePreview(certificateId: string) {
  try {
    console.log(
      `[getCertificatePreview] Starting preview generation for certificate: ${certificateId}`
    );

    // Vérifier que l'ID du certificat est valide
    if (!certificateId || typeof certificateId !== "string") {
      throw new Error("ID de certificat invalide");
    }

    // Générer le PDF avec timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(
          new Error("Timeout: La génération PDF a pris trop de temps (>25s)")
        );
      }, 25000); // 25 secondes de timeout
    });

    const pdfPromise = PdfGenerator.generateCertificatePdf(certificateId);

    console.log(`[getCertificatePreview] Waiting for PDF generation...`);
    const pdfBuffer = await Promise.race([pdfPromise, timeoutPromise]);

    // Vérifier que le buffer est valide
    if (!pdfBuffer || pdfBuffer.length === 0) {
      throw new Error("Le PDF généré est vide ou invalide");
    }

    console.log(
      `[getCertificatePreview] PDF generated successfully, size: ${pdfBuffer.length} bytes`
    );

    // Convertir le buffer en base64
    const base64Data = Buffer.from(pdfBuffer).toString("base64");

    // Vérifier que la conversion base64 a réussi
    if (!base64Data || base64Data.length === 0) {
      throw new Error("Erreur lors de la conversion en base64");
    }

    const previewUrl = `data:application/pdf;base64,${base64Data}`;

    console.log(
      `[getCertificatePreview] Preview URL generated successfully, length: ${previewUrl.length}`
    );

    return {
      success: true,
      previewUrl,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Erreur inconnue";
    console.error(
      `[getCertificatePreview] Error generating preview for certificate ${certificateId}:`,
      errorMessage
    );

    return {
      success: false,
      error: errorMessage,
    };
  }
}
