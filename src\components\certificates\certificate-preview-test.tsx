"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CertificatePreview } from "./certificate-preview";
import { Certificate } from "@/types/certificate";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";

// Mock certificate data for testing
const mockCertificate: Certificate = {
  $id: "68376fde001bb2d00fcc",
  reference: "NCR-2024-TEST-001",
  status: CERTIFICATE_STATUS.SIGNED,
  citizenId: "test-citizen-id",
  chefId: "test-chef-id",
  agentId: "test-agent-id",
  motif: "Test de prévisualisation",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  documentUrl: null,
  downloads: [],
  downloadedAt: null,
  verifiedBy: null,
  verifiedAt: null,
  verificationNotes: null,
  deliveredAt: null,
  deliveredBy: null,
  signedAt: new Date().toISOString(),
  signedBy: "test-chef-id",
};

export function CertificatePreviewTest() {
  const [isDelivered, setIsDelivered] = useState(false);
  const [certificateStatus, setCertificateStatus] =
    useState<CERTIFICATE_STATUS>(CERTIFICATE_STATUS.SIGNED);
  const [diagnostics, setDiagnostics] = useState<string[]>([]);

  const testCertificate = {
    ...mockCertificate,
    status: certificateStatus,
  };

  const addDiagnostic = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDiagnostics((prev) => [...prev, `[${timestamp}] ${message}`]);
  };

  const clearDiagnostics = () => {
    setDiagnostics([]);
  };

  const testDirectGeneration = async () => {
    addDiagnostic("🧪 Test de génération directe...");
    try {
      const startTime = Date.now();
      const response = await fetch("/api/test-pdf", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ certificateId: testCertificate.$id }),
      });

      const result = await response.json();
      const clientDuration = Date.now() - startTime;

      if (result.success) {
        addDiagnostic("✅ Génération directe réussie");
        addDiagnostic(`📏 Taille PDF: ${(result.size / 1024).toFixed(1)} KB`);
        addDiagnostic(`⏱️ Durée serveur: ${result.duration}ms`);
        addDiagnostic(`⏱️ Durée client: ${clientDuration}ms`);
        addDiagnostic(
          `📊 Données PDF: ${result.hasPdfData ? "Présentes" : "Absentes"}`
        );
        addDiagnostic(`📋 Type données: ${result.pdfDataType}`);
        addDiagnostic(`🔢 Longueur tableau: ${result.pdfDataLength}`);
      } else {
        addDiagnostic(`❌ Génération directe échouée: ${result.error}`);
        addDiagnostic(
          `⏱️ Durée avant échec: ${result.duration || clientDuration}ms`
        );
      }
    } catch (error) {
      addDiagnostic(`💥 Erreur lors du test direct: ${error}`);
    }
  };

  const testRealCertificate = async () => {
    addDiagnostic("🔍 Test avec un vrai certificat...");
    // Utiliser un ID de certificat réel si disponible
    const realCertificateId = "68376fbf001cd2123bf5"; // ID du certificat vu dans les logs

    try {
      const startTime = Date.now();
      const response = await fetch("/api/test-pdf", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ certificateId: realCertificateId }),
      });

      const result = await response.json();
      const clientDuration = Date.now() - startTime;

      if (result.success) {
        addDiagnostic("✅ Test avec vrai certificat réussi");
        addDiagnostic(`📏 Taille PDF: ${(result.size / 1024).toFixed(1)} KB`);
        addDiagnostic(`⏱️ Durée serveur: ${result.duration}ms`);
        addDiagnostic(`⏱️ Durée client: ${clientDuration}ms`);
        addDiagnostic(
          `📊 Données PDF: ${result.hasPdfData ? "Présentes" : "Absentes"}`
        );
        addDiagnostic(`🔢 Longueur tableau: ${result.pdfDataLength}`);
      } else {
        addDiagnostic(`❌ Test avec vrai certificat échoué: ${result.error}`);
      }
    } catch (error) {
      addDiagnostic(`💥 Erreur lors du test avec vrai certificat: ${error}`);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Test de Prévisualisation des Certificats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              variant={isDelivered ? "default" : "outline"}
              onClick={() => setIsDelivered(true)}
            >
              Mode Délivré
            </Button>
            <Button
              variant={!isDelivered ? "default" : "outline"}
              onClick={() => setIsDelivered(false)}
            >
              Mode Prévisualisation
            </Button>
          </div>

          <div className="flex gap-4">
            <Button
              variant={
                certificateStatus === CERTIFICATE_STATUS.SIGNED
                  ? "default"
                  : "outline"
              }
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.SIGNED)}
            >
              Statut: Signé
            </Button>
            <Button
              variant={
                certificateStatus === CERTIFICATE_STATUS.DELIVERED
                  ? "default"
                  : "outline"
              }
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.DELIVERED)}
            >
              Statut: Délivré
            </Button>
            <Button
              variant={
                certificateStatus === CERTIFICATE_STATUS.PENDING
                  ? "default"
                  : "outline"
              }
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.PENDING)}
            >
              Statut: En attente
            </Button>
          </div>

          <div className="flex gap-4">
            <Button onClick={testDirectGeneration} variant="secondary">
              🧪 Test Direct (Mock)
            </Button>
            <Button onClick={testRealCertificate} variant="secondary">
              🔍 Test Vrai Certificat
            </Button>
            <Button onClick={clearDiagnostics} variant="outline">
              🗑️ Effacer Logs
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p>Certificat ID: {testCertificate.$id}</p>
            <p>Référence: {testCertificate.reference}</p>
            <p>Statut: {testCertificate.status}</p>
            <p>Mode: {isDelivered ? "Délivré" : "Prévisualisation"}</p>
          </div>

          {diagnostics.length > 0 && (
            <div className="bg-gray-100 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Diagnostics en Temps Réel:</h4>
              <div className="text-xs font-mono space-y-1 max-h-60 overflow-y-auto">
                {diagnostics.map((log, index) => (
                  <div key={index} className="text-gray-700">
                    {log}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <CertificatePreview
        certificate={testCertificate}
        isDelivered={isDelivered}
      />
    </div>
  );
}
