"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CertificatePreview } from "./certificate-preview";
import { Certificate } from "@/types/certificate";
import { CERTIFICATE_STATUS } from "@/actions/auth/constants";

// Mock certificate data for testing
const mockCertificate: Certificate = {
  $id: "test-certificate-id",
  reference: "NCR-2024-001",
  status: CERTIFICATE_STATUS.SIGNED,
  citizenId: "test-citizen-id",
  chefId: "test-chef-id",
  agentId: "test-agent-id",
  motif: "Demande d'emploi",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  documentUrl: null,
  downloads: [],
  downloadedAt: null,
  verifiedBy: null,
  verifiedAt: null,
  verificationNotes: null,
  deliveredAt: null,
  deliveredBy: null,
  signedAt: new Date().toISOString(),
  signedBy: "test-chef-id",
};

export function CertificatePreviewTest() {
  const [isDelivered, setIsDelivered] = useState(false);
  const [certificateStatus, setCertificateStatus] = useState<CERTIFICATE_STATUS>(
    CERTIFICATE_STATUS.SIGNED
  );

  const testCertificate = {
    ...mockCertificate,
    status: certificateStatus,
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Test de Prévisualisation des Certificats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              variant={isDelivered ? "default" : "outline"}
              onClick={() => setIsDelivered(true)}
            >
              Mode Délivré
            </Button>
            <Button
              variant={!isDelivered ? "default" : "outline"}
              onClick={() => setIsDelivered(false)}
            >
              Mode Prévisualisation
            </Button>
          </div>

          <div className="flex gap-4">
            <Button
              variant={certificateStatus === CERTIFICATE_STATUS.SIGNED ? "default" : "outline"}
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.SIGNED)}
            >
              Statut: Signé
            </Button>
            <Button
              variant={certificateStatus === CERTIFICATE_STATUS.DELIVERED ? "default" : "outline"}
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.DELIVERED)}
            >
              Statut: Délivré
            </Button>
            <Button
              variant={certificateStatus === CERTIFICATE_STATUS.PENDING ? "default" : "outline"}
              onClick={() => setCertificateStatus(CERTIFICATE_STATUS.PENDING)}
            >
              Statut: En attente
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p>Certificat ID: {testCertificate.$id}</p>
            <p>Référence: {testCertificate.reference}</p>
            <p>Statut: {testCertificate.status}</p>
            <p>Mode: {isDelivered ? "Délivré" : "Prévisualisation"}</p>
          </div>
        </CardContent>
      </Card>

      <CertificatePreview 
        certificate={testCertificate}
        isDelivered={isDelivered}
      />
    </div>
  );
}
