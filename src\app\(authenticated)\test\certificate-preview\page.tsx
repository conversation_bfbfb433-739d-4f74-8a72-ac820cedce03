"use client";

import { CertificatePreviewTest } from "@/components/certificates/certificate-preview-test";
import { DashboardHeader } from "@/components/dashboard/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function CertificatePreviewTestPage() {
  return (
    <div className="space-y-8 p-8">
      <DashboardHeader
        heading="Test de Prévisualisation des Certificats"
        text="Page de test pour vérifier le bon fonctionnement de la prévisualisation des certificats avec la nouvelle solution Blob URL"
      />

      {/* Informations sur la solution */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔧 Nouvelle Solution Implémentée
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Blob URLs
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-green-800 mb-2">
                ✅ Problème Résolu :
              </h4>
              <ul className="space-y-1 text-green-700">
                <li>• URLs de prévisualisation trop grandes (28MB)</li>
                <li>• Limites de Next.js dépassées</li>
                <li>• Échec de transmission côté client</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-green-800 mb-2">
                🚀 Nouvelle Approche :
              </h4>
              <ul className="space-y-1 text-green-700">
                <li>• Transmission de données binaires compactes</li>
                <li>• Création de Blob URLs côté client</li>
                <li>• Performance et mémoire optimisées</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-white rounded border border-green-200">
            <p className="text-sm text-green-800">
              <strong>Comment ça marche :</strong> Le serveur génère le PDF et
              retourne un tableau d'octets. Le client crée ensuite une Blob URL
              pour afficher le PDF dans l'iframe.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Instructions de test */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Instructions de Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-blue-800">
            <ol className="list-decimal list-inside space-y-2">
              <li>
                Utilisez les boutons pour changer le mode et le statut du
                certificat
              </li>
              <li>
                Cliquez sur "🧪 Test Direct" pour tester l'API directement
              </li>
              <li>Observez les logs de diagnostic en temps réel</li>
              <li>Vérifiez que la prévisualisation s'affiche correctement</li>
              <li>Testez les fonctionnalités de retry et de cache</li>
            </ol>
          </div>
          <div className="mt-4 p-3 bg-white rounded border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Attendu :</strong> La prévisualisation devrait se charger
              rapidement avec une Blob URL (blob:http://localhost:3000/...) et
              afficher le PDF dans l'iframe.
            </p>
          </div>
        </CardContent>
      </Card>

      <CertificatePreviewTest />
    </div>
  );
}
