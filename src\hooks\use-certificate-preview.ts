"use client";

import { getCertificatePreview } from "@/actions/certificates";
import { useCallback, useRef, useState } from "react";

interface PreviewCache {
  [certificateId: string]: {
    url: string;
    timestamp: number;
    status: string;
  };
}

export function useCertificatePreview() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [progress, setProgress] = useState<string>("");
  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef<PreviewCache>({});
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cache TTL: 5 minutes
  const CACHE_TTL = 5 * 60 * 1000;

  // Timeout pour la génération: 30 secondes
  const GENERATION_TIMEOUT = 30000;

  // Nombre maximum de tentatives
  const MAX_RETRIES = 3;

  const generatePreview = useCallback(
    async (
      certificateId: string,
      certificateStatus?: string,
      forceRefresh = false,
      currentRetry = 0
    ): Promise<string | null> => {
      // Annuler toute génération en cours
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Nettoyer le timeout précédent
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Vérifier le cache si pas de force refresh
      if (!forceRefresh && currentRetry === 0) {
        const cached = cacheRef.current[certificateId];
        if (
          cached &&
          cached.status === certificateStatus &&
          Date.now() - cached.timestamp < CACHE_TTL
        ) {
          console.log("Using cached preview for certificate:", certificateId);
          setRetryCount(0);
          setProgress("");
          return cached.url;
        }
      }

      // Créer un nouveau AbortController
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      setIsGenerating(true);
      setError(null);
      setRetryCount(currentRetry);
      setProgress(
        currentRetry > 0
          ? `Tentative ${currentRetry + 1}/${MAX_RETRIES}...`
          : "Génération en cours..."
      );

      try {
        console.log(
          `Generating preview for certificate: ${certificateId}, attempt: ${
            currentRetry + 1
          }`
        );

        // Créer une promesse avec timeout robuste
        const timeoutPromise = new Promise<never>((_, reject) => {
          timeoutRef.current = setTimeout(() => {
            reject(
              new Error(
                `Timeout: La génération a pris plus de ${
                  GENERATION_TIMEOUT / 1000
                }s (tentative ${currentRetry + 1})`
              )
            );
          }, GENERATION_TIMEOUT);
        });

        // Créer la promesse de génération avec indicateur de progression
        setProgress(
          currentRetry > 0
            ? `Tentative ${currentRetry + 1}/${MAX_RETRIES} - Génération PDF...`
            : "Génération du PDF..."
        );

        const generationPromise = getCertificatePreview(certificateId);

        // Course entre timeout et génération
        const response = await Promise.race([
          generationPromise,
          timeoutPromise,
        ]);

        // Nettoyer le timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Vérifier si l'opération a été annulée
        if (abortController.signal.aborted) {
          console.log(
            "Preview generation aborted for certificate:",
            certificateId
          );
          return null;
        }

        if (!response.success) {
          throw new Error(
            response.error ||
              "Erreur lors de la génération de la prévisualisation"
          );
        }

        // Vérifier que les données PDF existent
        if (
          !response.pdfData ||
          !Array.isArray(response.pdfData) ||
          response.pdfData.length === 0
        ) {
          throw new Error("Aucune donnée PDF retournée par le serveur");
        }

        setProgress("Création de la prévisualisation...");

        console.log(
          `Creating blob from PDF data, size: ${response.size} bytes`
        );

        // Créer un Blob à partir des données PDF
        const uint8Array = new Uint8Array(response.pdfData);
        const blob = new Blob([uint8Array], { type: "application/pdf" });

        // Créer une URL blob
        const blobUrl = URL.createObjectURL(blob);

        console.log(`Blob URL created: ${blobUrl}`);

        // Mettre en cache le résultat
        cacheRef.current[certificateId] = {
          url: blobUrl,
          timestamp: Date.now(),
          status: certificateStatus || "unknown",
        };

        // Reset des états de retry
        setRetryCount(0);
        setProgress("");

        console.log(
          "Preview generated successfully for certificate:",
          certificateId
        );
        return blobUrl;
      } catch (err) {
        // Nettoyer le timeout en cas d'erreur
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Ne pas traiter l'erreur si l'opération a été annulée
        if (abortController.signal.aborted) {
          return null;
        }

        const errorMessage =
          err instanceof Error ? err.message : "Erreur inconnue";
        console.error(
          `Error generating preview (attempt ${currentRetry + 1}):`,
          errorMessage
        );

        // Retry automatique avec backoff exponentiel
        if (currentRetry < MAX_RETRIES - 1) {
          const delay = Math.min(1000 * Math.pow(2, currentRetry), 8000); // Max 8s
          console.log(
            `Retrying in ${delay}ms (attempt ${
              currentRetry + 2
            }/${MAX_RETRIES})`
          );

          setProgress(
            `Erreur détectée. Nouvelle tentative dans ${Math.ceil(
              delay / 1000
            )}s...`
          );

          // Attendre avant de retry
          await new Promise((resolve) => setTimeout(resolve, delay));

          // Vérifier si pas annulé pendant l'attente
          if (!abortController.signal.aborted) {
            return generatePreview(
              certificateId,
              certificateStatus,
              true,
              currentRetry + 1
            );
          }
        } else {
          // Toutes les tentatives ont échoué
          setError(`Échec après ${MAX_RETRIES} tentatives: ${errorMessage}`);
          setProgress("");
        }

        return null;
      } finally {
        // Ne pas changer l'état si l'opération a été annulée
        if (!abortController.signal.aborted) {
          setIsGenerating(false);
          if (!timeoutRef.current) {
            setProgress("");
          }
        }

        // Nettoyer la référence si c'est le bon controller
        if (abortControllerRef.current === abortController) {
          abortControllerRef.current = null;
        }
      }
    },
    []
  );

  const cleanupPreview = useCallback((url: string) => {
    if (url.startsWith("blob:")) {
      URL.revokeObjectURL(url);
    }
  }, []);

  const clearCache = useCallback(
    (certificateId?: string) => {
      if (certificateId) {
        // Nettoyer l'URL du cache avant de supprimer
        const cached = cacheRef.current[certificateId];
        if (cached) {
          cleanupPreview(cached.url);
          delete cacheRef.current[certificateId];
        }
      } else {
        // Nettoyer tout le cache
        Object.values(cacheRef.current).forEach((cached) => {
          cleanupPreview(cached.url);
        });
        cacheRef.current = {};
      }
    },
    [cleanupPreview]
  );

  const cancelGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    setIsGenerating(false);
    setError(null);
    setRetryCount(0);
    setProgress("");
  }, []);

  const resetState = useCallback(() => {
    cancelGeneration();
    setError(null);
    setRetryCount(0);
    setProgress("");
  }, [cancelGeneration]);

  return {
    generatePreview,
    cleanupPreview,
    clearCache,
    cancelGeneration,
    resetState,
    isGenerating,
    error,
    retryCount,
    progress,
    maxRetries: MAX_RETRIES,
    clearError: () => setError(null),
  };
}
