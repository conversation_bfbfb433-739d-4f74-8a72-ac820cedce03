"use client";

import { getCertificatePreview } from "@/actions/certificates";
import { useCallback, useRef, useState } from "react";

interface PreviewCache {
  [certificateId: string]: {
    url: string;
    timestamp: number;
    status: string;
  };
}

export function useCertificatePreview() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef<PreviewCache>({});

  // Cache TTL: 5 minutes
  const CACHE_TTL = 5 * 60 * 1000;

  // Timeout pour la génération: 30 secondes
  const GENERATION_TIMEOUT = 30000;

  const generatePreview = useCallback(
    async (
      certificateId: string,
      certificateStatus?: string,
      forceRefresh = false
    ): Promise<string | null> => {
      // Annuler toute génération en cours
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Vérifier le cache si pas de force refresh
      if (!forceRefresh) {
        const cached = cacheRef.current[certificateId];
        if (
          cached &&
          cached.status === certificateStatus &&
          Date.now() - cached.timestamp < CACHE_TTL
        ) {
          console.log("Using cached preview for certificate:", certificateId);
          return cached.url;
        }
      }

      // Créer un nouveau AbortController
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      setIsGenerating(true);
      setError(null);

      try {
        console.log("Generating preview for certificate:", certificateId);

        // Créer une promesse avec timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(
              new Error(
                "Timeout: La génération de la prévisualisation a pris trop de temps"
              )
            );
          }, GENERATION_TIMEOUT);
        });

        // Créer la promesse de génération
        const generationPromise = getCertificatePreview(certificateId);

        // Course entre timeout et génération
        const response = await Promise.race([
          generationPromise,
          timeoutPromise,
        ]);

        // Vérifier si l'opération a été annulée
        if (abortController.signal.aborted) {
          console.log(
            "Preview generation aborted for certificate:",
            certificateId
          );
          return null;
        }

        if (!response.success) {
          throw new Error(
            response.error ||
              "Erreur lors de la génération de la prévisualisation"
          );
        }

        // Vérifier que previewUrl existe et n'est pas vide
        if (!response.previewUrl || response.previewUrl.trim() === "") {
          throw new Error(
            "Aucune URL de prévisualisation retournée par le serveur"
          );
        }

        // Vérifier que l'URL est valide (data URL ou blob URL)
        if (
          !response.previewUrl.startsWith("data:") &&
          !response.previewUrl.startsWith("blob:")
        ) {
          throw new Error("URL de prévisualisation invalide");
        }

        // Mettre en cache le résultat
        cacheRef.current[certificateId] = {
          url: response.previewUrl,
          timestamp: Date.now(),
          status: certificateStatus || "unknown",
        };

        console.log(
          "Preview generated successfully for certificate:",
          certificateId
        );
        return response.previewUrl;
      } catch (err) {
        // Ne pas définir l'erreur si l'opération a été annulée
        if (!abortController.signal.aborted) {
          const errorMessage =
            err instanceof Error ? err.message : "Erreur inconnue";
          console.error("Error generating preview:", errorMessage);
          setError(errorMessage);
        }
        return null;
      } finally {
        // Ne pas changer l'état si l'opération a été annulée
        if (!abortController.signal.aborted) {
          setIsGenerating(false);
        }

        // Nettoyer la référence si c'est le bon controller
        if (abortControllerRef.current === abortController) {
          abortControllerRef.current = null;
        }
      }
    },
    []
  );

  const cleanupPreview = useCallback((url: string) => {
    if (url.startsWith("blob:")) {
      URL.revokeObjectURL(url);
    }
  }, []);

  const clearCache = useCallback(
    (certificateId?: string) => {
      if (certificateId) {
        // Nettoyer l'URL du cache avant de supprimer
        const cached = cacheRef.current[certificateId];
        if (cached) {
          cleanupPreview(cached.url);
          delete cacheRef.current[certificateId];
        }
      } else {
        // Nettoyer tout le cache
        Object.values(cacheRef.current).forEach((cached) => {
          cleanupPreview(cached.url);
        });
        cacheRef.current = {};
      }
    },
    [cleanupPreview]
  );

  const cancelGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      setIsGenerating(false);
      setError(null);
    }
  }, []);

  return {
    generatePreview,
    cleanupPreview,
    clearCache,
    cancelGeneration,
    isGenerating,
    error,
    clearError: () => setError(null),
  };
}
