/**
 * Test script for certificate preview generation
 * Tests the specific certificate ID mentioned in the issue: 68376fde001bb2d00fcc
 */

import { getCertificatePreview } from "@/actions/certificates";

const TEST_CERTIFICATE_ID = "68376fde001bb2d00fcc";

async function testCertificatePreview() {
  console.log("🧪 Testing Certificate Preview Generation");
  console.log("==========================================");
  console.log(`Certificate ID: ${TEST_CERTIFICATE_ID}`);
  console.log("");

  const startTime = Date.now();
  let attempt = 1;
  const maxAttempts = 3;

  while (attempt <= maxAttempts) {
    console.log(`📋 Attempt ${attempt}/${maxAttempts}`);
    console.log(`⏰ Started at: ${new Date().toISOString()}`);

    try {
      // Test with timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Timeout after 30 seconds (attempt ${attempt})`));
        }, 30000);
      });

      const generationPromise = getCertificatePreview(TEST_CERTIFICATE_ID);

      const result = await Promise.race([generationPromise, timeoutPromise]);

      if (result.success) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log("✅ SUCCESS!");
        console.log(`📊 Duration: ${duration}ms`);
        console.log(`📄 PDF Size: ${result.size} bytes`);
        console.log(`🔢 PDF Data Length: ${result.pdfData?.length || 0}`);
        
        // Verify PDF data integrity
        if (result.pdfData && Array.isArray(result.pdfData) && result.pdfData.length > 0) {
          console.log("✅ PDF data is valid");
          
          // Test blob creation
          try {
            const uint8Array = new Uint8Array(result.pdfData);
            const blob = new Blob([uint8Array], { type: "application/pdf" });
            console.log(`✅ Blob created successfully: ${blob.size} bytes`);
          } catch (blobError) {
            console.error("❌ Blob creation failed:", blobError);
          }
        } else {
          console.error("❌ Invalid PDF data");
        }
        
        return result;
      } else {
        throw new Error(result.error || "Unknown error");
      }
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`❌ FAILED (attempt ${attempt})`);
      console.log(`📊 Duration: ${duration}ms`);
      console.log(`🚨 Error: ${error instanceof Error ? error.message : String(error)}`);

      if (attempt < maxAttempts) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 8000);
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      attempt++;
    }
  }

  console.log("");
  console.log("❌ All attempts failed");
  console.log("🔧 Recommendations:");
  console.log("   1. Check certificate data integrity");
  console.log("   2. Verify PDF generator service");
  console.log("   3. Check network connectivity");
  console.log("   4. Review server logs");
}

// Export for use in other modules
export { testCertificatePreview, TEST_CERTIFICATE_ID };

// Run if called directly
if (require.main === module) {
  testCertificatePreview()
    .then(() => {
      console.log("🏁 Test completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Test script failed:", error);
      process.exit(1);
    });
}
