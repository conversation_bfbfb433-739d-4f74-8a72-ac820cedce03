import { getCertificatePreview } from "@/actions/certificates";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { certificateId } = await request.json();
    
    console.log(`[API Test] Testing PDF generation for certificate: ${certificateId}`);
    
    if (!certificateId) {
      return NextResponse.json({
        success: false,
        error: "Certificate ID is required"
      }, { status: 400 });
    }

    const startTime = Date.now();
    const result = await getCertificatePreview(certificateId);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    
    console.log(`[API Test] Generation completed in ${duration}ms`);
    
    if (result.success) {
      const size = result.size || 0;
      const hasPdfData = !!result.pdfData;
      const pdfDataLength = result.pdfData?.length || 0;
      
      return NextResponse.json({
        success: true,
        size,
        duration,
        hasPdfData,
        pdfDataLength,
        pdfDataType: Array.isArray(result.pdfData) ? "array" : typeof result.pdfData,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
        duration,
      });
    }
  } catch (error) {
    console.error("[API Test] Error:", error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }, { status: 500 });
  }
}
