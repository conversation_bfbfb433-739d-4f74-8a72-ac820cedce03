import { getCertificateWithFullData } from "@/actions/certificates";
import { Certificate } from "@/actions/types";
import { CertificateVerificationService } from "@/lib/database/certificate-verification";
import { createAdminClient } from "@/lib/server/appwrite";
import { SIGNATURES_BUCKET_ID } from "@/lib/server/storage";
import {
  CertificateCrypto,
  CertificateHashData,
} from "@/lib/utils/certificate-crypto";
import { QRCodeGenerator } from "@/lib/utils/qr-generator";
import { jsPDF } from "jspdf";

const COLORS_V2 = {
  RED: "#CE1126",
  YELLOW: "#FCD116",
  GREEN: "#009639",
  DARK_GREEN: "#006B2F",
  LIGHT_GRAY: "#F8F9FA",
  DARK_GRAY: "#343A40",
  BLACK: "#000000",
  WHITE: "#FFFFFF",
};

const DESIGN_V2 = {
  PAGE_MARGIN: 10,
  BORDER_WIDTH: 2,
  HEADER_HEIGHT: 65,
  FOOTER_HEIGHT: 45,
  CONTENT_PADDING: 12,
  SECURITY_OPACITY: 0.1,
  QR_SIZE: 20,
  QR_MARGIN: 3,
};

export class PdfGeneratorV2Enhanced {
  private static baseUrl = process.env.NEXT_PUBLIC_APP_URL!;
  private static brandingBase64: string;
  private static armoirieBase64: string;
  private static simandouBase64: string;

  private static async loadImage(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      return base64;
    } catch (error) {
      console.error(`Erreur lors du chargement de l'image ${url}:`, error);
      return "";
    }
  }

  static async generateCertificatePdf(certificateId: string): Promise<Buffer> {
    const { certificate } = await getCertificateWithFullData(certificateId);
    const { storage } = await createAdminClient();

    // Debug logging to verify data availability
    console.log("=== PDF Generator V2 Enhanced Debug ===");
    console.log("Certificate ID:", certificateId);
    console.log("Certificate Reference:", certificate.reference);
    console.log("Citizen Data:", {
      name: certificate.citizenName,
      numeroIdentificationUnique:
        certificate.citizen?.numeroIdentificationUnique,
      profession: certificate.citizen?.profession,
      dateNaissance: certificate.citizen?.dateNaissance,
      lieuNaissance: certificate.citizen?.lieuNaissance,
      nomPere: certificate.citizen?.nomPere,
      nomMere: certificate.citizen?.nomMere,
      adressePrecise: certificate.citizen?.adressePrecise,
      dateInstallation: certificate.citizen?.dateInstallation,
      carteElecteur: certificate.citizen?.carteElecteur,
    });
    console.log("Quartier Data:", {
      nom: certificate.quartier?.nom,
      commune: certificate.quartier?.commune,
      region: certificate.quartier?.region,
    });
    console.log("Chef Data:", {
      name: certificate.chefQuartierName,
    });
    console.log("Certificate Motif:", certificate.motif);
    console.log("======================================");

    // Chargement des images
    if (!this.armoirieBase64) {
      this.armoirieBase64 = await this.loadImage(
        `${this.baseUrl}/images/armoirie.png`
      );
    }

    if (!this.brandingBase64) {
      this.brandingBase64 = await this.loadImage(
        `${this.baseUrl}/images/branding.png`
      );
    }

    if (!this.simandouBase64) {
      this.simandouBase64 = await this.loadImage(
        `${this.baseUrl}/images/simandou.png`
      );
    }

    // Génération des données de sécurité
    const securityData = await this.generateSecurityData(certificate);

    // Création du document PDF
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Construction du document moderne avec sécurité
    this.addModernBorder(doc);
    this.addModernHeader(doc, certificate);
    this.addSecurityElements(doc, certificate, securityData);
    this.addModernContent(doc, certificate, securityData);
    await this.addQRCode(doc, securityData);
    this.addModernFooter(doc, certificate);

    // Ajout de la signature si disponible
    if (certificate.signatureFileId) {
      try {
        const signatureFile = await storage.getFileDownload(
          SIGNATURES_BUCKET_ID,
          certificate.signatureFileId
        );
        await this.addModernSignature(doc, signatureFile, certificate);
      } catch (error) {
        console.error("Erreur lors de la récupération de la signature:", error);
      }
    }

    return Buffer.from(doc.output("arraybuffer"));
  }

  private static async generateSecurityData(certificate: Certificate) {
    // Préparation des données pour le hash
    const hashData: CertificateHashData = {
      certificateId: certificate.$id,
      citizenId: certificate.citizenId,
      citizenIdUnique: certificate.citizen.numeroIdentificationUnique || "",
      timestamp: Date.now(),
      reference: certificate.reference,
      issuerInfo: {
        type: "chef",
        id: certificate.chefId,
        name: certificate.chefQuartierName || "",
      },
      locationInfo: {
        region: certificate.quartier?.region || "",
        commune: certificate.quartier?.commune || "",
        quartier: certificate.quartier?.nom || "",
      },
    };

    // Génération des données de sécurité
    const security = CertificateCrypto.generateCertificateSecurity(hashData);

    // Création de l'enregistrement de vérification en base
    try {
      const issuedAt = new Date();
      const expiresAt = new Date();
      expiresAt.setMonth(expiresAt.getMonth() + 3); // Validité 3 mois

      await CertificateVerificationService.createVerification({
        hash: security.verificationHash,
        certificateId: certificate.$id,
        citizenId: certificate.citizenId,
        issuedAt,
        expiresAt,
        metadata: {
          issuerType: hashData.issuerInfo.type,
          issuerId: hashData.issuerInfo.id,
          issuerName: hashData.issuerInfo.name,
          region: hashData.locationInfo.region,
          commune: hashData.locationInfo.commune,
          quartier: hashData.locationInfo.quartier,
        },
      });
    } catch (error) {
      console.error(
        "Erreur lors de la création de l'enregistrement de vérification:",
        error
      );
    }

    return security;
  }

  private static addModernBorder(doc: jsPDF) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN;
    const borderWidth = DESIGN_V2.BORDER_WIDTH;

    // Bordure principale verte
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(borderWidth);
    doc.rect(margin, margin, pageWidth - 2 * margin, pageHeight - 2 * margin);

    // Bande tricolore en haut
    const bandHeight = 8;
    const bandWidth = pageWidth - 2 * margin - 2 * borderWidth;
    const bandX = margin + borderWidth;
    const bandY = margin + borderWidth;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(bandX + bandWidth / 3, bandY, bandWidth / 3, bandHeight, "F");

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Bande tricolore en bas
    const bottomBandY = pageHeight - margin - borderWidth - bandHeight;

    // Rouge
    doc.setFillColor(COLORS_V2.RED);
    doc.rect(bandX, bottomBandY, bandWidth / 3, bandHeight, "F");

    // Jaune
    doc.setFillColor(COLORS_V2.YELLOW);
    doc.rect(
      bandX + bandWidth / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );

    // Vert
    doc.setFillColor(COLORS_V2.GREEN);
    doc.rect(
      bandX + (2 * bandWidth) / 3,
      bottomBandY,
      bandWidth / 3,
      bandHeight,
      "F"
    );
  }

  private static addModernHeader(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const headerY = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH + 10;
    let currentY = headerY;

    // Armoirie au centre
    if (this.armoirieBase64) {
      const logoSize = 20;
      doc.addImage(
        `data:image/png;base64,${this.armoirieBase64}`,
        "PNG",
        pageWidth / 2 - logoSize / 2,
        currentY,
        logoSize,
        logoSize
      );
    }
    currentY += 25;

    // République de Guinée
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text("RÉPUBLIQUE DE GUINÉE", pageWidth / 2, currentY, {
      align: "center",
    });
    currentY += 12;

    // Titre principal
    doc.setFontSize(20);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("CERTIFICAT", pageWidth / 2, currentY, { align: "center" });
    currentY += 8;
    doc.text("DE RÉSIDENCE", pageWidth / 2, currentY, { align: "center" });
    currentY += 10;

    // Numéro de certificat centré sous le titre
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(`N° ${certificate.reference}`, pageWidth / 2, currentY, {
      align: "center",
    });
    currentY += 12;

    // Ligne de séparation
    doc.setDrawColor(COLORS_V2.DARK_GREEN);
    doc.setLineWidth(1);
    doc.line(pageWidth / 2 - 50, currentY, pageWidth / 2 + 50, currentY);
  }

  private static addSecurityElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Filigrane de sécurité - Watermark dynamique en arrière-plan
    doc.saveGraphicsState();
    doc.setGState(doc.GState({ opacity: DESIGN_V2.SECURITY_OPACITY }));
    doc.setFontSize(35); // Slightly smaller for better fit
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.GREEN);

    // Adjusted watermark positioning - moved right and lower for better balance
    const watermarkX = pageWidth / 2 + 12; // Moved right by 12mm
    const watermarkY = pageHeight / 2 + 35; // Moved down by 35mm for better positioning

    doc.text(securityData.watermark, watermarkX, watermarkY, {
      align: "center",
      angle: 45,
    });
    doc.restoreGraphicsState();

    // Éléments de sécurité visibles
    this.addSecurityBadge(doc, certificate);
    this.addVerificationElements(doc, certificate, securityData);
  }

  private static addSecurityBadge(doc: jsPDF, _certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const badgeX = pageWidth - 60;
    const badgeY = 48; // Moved down by 8mm to avoid timestamp overlap

    // Badge de sécurité
    doc.setFillColor(COLORS_V2.LIGHT_GRAY);
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.roundedRect(badgeX, badgeY, 45, 25, 3, 3, "FD");

    // Texte du badge
    doc.setFontSize(8);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text("SÉCURISÉ", badgeX + 22.5, badgeY + 8, { align: "center" });
    doc.text("GUINÉE", badgeX + 22.5, badgeY + 15, { align: "center" });
    doc.text("2024", badgeX + 22.5, badgeY + 22, { align: "center" });
  }

  private static addVerificationElements(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    // Référence du certificat en haut à droite
    const pageWidth = doc.internal.pageSize.width;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.DARK_GRAY);
    doc.text(`Réf: ${certificate.reference}`, pageWidth - 20, 30, {
      align: "right",
    });

    // Hash de vérification
    doc.setFontSize(8);
    doc.text(`Hash: ${securityData.displayHash}`, pageWidth - 20, 35, {
      align: "right",
    });

    // Horodatage cryptographique
    doc.text(
      `TS: ${securityData.timestamp.timestampHash}`,
      pageWidth - 20,
      40,
      { align: "right" }
    );
  }

  private static addModernContent(
    doc: jsPDF,
    certificate: Certificate,
    securityData: any
  ) {
    let currentY = 95; // Position après l'en-tête optimisée

    // ID Citoyen mis en valeur
    this.addCitizenIdSection(doc, certificate, currentY);
    currentY += 25; // Adjusted spacing for additional header content

    // Informations principales (now includes header and authority info)
    this.addMainInformation(doc, certificate, currentY);
    currentY += 85; // Increased to accommodate additional content

    // Informations détaillées
    this.addDetailedInformation(doc, certificate, currentY);
  }

  private static addCitizenIdSection(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const pageWidth = doc.internal.pageSize.width;

    // Encadré pour l'ID citoyen
    const boxWidth = 140;
    const boxHeight = 16;
    const boxX = pageWidth / 2 - boxWidth / 2;

    doc.setFillColor(COLORS_V2.LIGHT_GRAY);
    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.roundedRect(boxX, yPosition, boxWidth, boxHeight, 2, 2, "FD");

    // Texte ID Citoyen
    doc.setFontSize(10);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(
      `ID CITOYEN: ${certificate.citizen.numeroIdentificationUnique}`,
      pageWidth / 2,
      yPosition + 10,
      { align: "center" }
    );
  }

  private static addMainInformation(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const contentX = margin + DESIGN_V2.CONTENT_PADDING;
    let currentY = yPosition;

    // Header Information (before certificate number) - Professional horizontal layout
    doc.setFontSize(11);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.BLACK);

    console.log("=== Rendering Header Information Horizontally ===");
    console.log(`Position Y: ${currentY}, Content X: ${contentX}`);

    // Geographic information on single line: Région - Préfecture - District/Quartier
    const pageWidth = doc.internal.pageSize.width;
    const rightMargin =
      DESIGN_V2.PAGE_MARGIN +
      DESIGN_V2.BORDER_WIDTH +
      DESIGN_V2.CONTENT_PADDING;
    const availableWidth = pageWidth - contentX - rightMargin;

    // Calculate positions for three-column layout
    const columnWidth = availableWidth / 3;
    const col1X = contentX;
    const col2X = contentX + columnWidth;
    const col3X = contentX + 2 * columnWidth;

    // Column 1: Région
    doc.setFont("helvetica", "bold");
    doc.text("Région de: ", col1X, currentY);
    doc.setFont("helvetica", "normal");
    const regionLabelWidth = doc.getTextWidth("Région de: ");
    const regionValue = certificate.quartier?.region || "NON DÉFINI";
    doc.text(regionValue, col1X + regionLabelWidth + 2, currentY);

    // Column 2: Préfecture
    doc.setFont("helvetica", "bold");
    doc.text("Préfecture de: ", col2X, currentY);
    doc.setFont("helvetica", "normal");
    const prefectureLabelWidth = doc.getTextWidth("Préfecture de: ");
    const prefectureValue = certificate.quartier?.commune || "NON DÉFINI";
    doc.text(prefectureValue, col2X + prefectureLabelWidth + 2, currentY);

    // Column 3: District/Quartier
    doc.setFont("helvetica", "bold");
    doc.text("District/Quartier: ", col3X, currentY);
    doc.setFont("helvetica", "normal");
    const quartierLabelWidth = doc.getTextWidth("District/Quartier: ");
    const quartierValue = certificate.quartier?.nom || "NON DÉFINI";
    doc.text(quartierValue, col3X + quartierLabelWidth + 2, currentY);

    console.log(
      `Geographic info rendered on single line: ${regionValue} | ${prefectureValue} | ${quartierValue}`
    );
    currentY += 12;

    // Date d'émission (with bold label)
    doc.setFontSize(11);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.BLACK);
    doc.text("Date d'émission: ", contentX, currentY);

    doc.setFont("helvetica", "normal");
    const dateLabelWidth = doc.getTextWidth("Date d'émission: ");
    const dateStr = new Date().toLocaleDateString("fr-FR");
    doc.text(`Le ${dateStr}`, contentX + dateLabelWidth + 4, currentY); // Added 4mm spacing
    currentY += 12;

    // Authority Information (after date, before "Certifie que") - Enhanced visibility
    console.log("=== Rendering Authority Information ===");
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.BLACK); // Ensure black color for visibility
    doc.text("Je soussigné M./Mme.: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const chefWidth = doc.getTextWidth("Je soussigné M./Mme.: ");
    const chefValue = certificate.chefQuartierName || "NON DÉFINI";
    doc.text(chefValue, contentX + chefWidth + 4, currentY);
    console.log(`Chef name rendered: "${chefValue}" at Y: ${currentY}`);
    currentY += 8;

    doc.setFont("helvetica", "bold");
    doc.text("Président du conseil de quartier de: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const presidentWidth = doc.getTextWidth(
      "Président du conseil de quartier de: "
    );
    const presidentQuartierValue = certificate.quartier?.nom || "NON DÉFINI";
    doc.text(presidentQuartierValue, contentX + presidentWidth + 4, currentY);
    console.log(
      `President quartier rendered: "${presidentQuartierValue}" at Y: ${currentY}`
    );
    currentY += 12;

    // Main certification text (with bold labels)
    doc.setFont("helvetica", "bold");
    doc.text("Certifie que: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const certifieLabelWidth = doc.getTextWidth("Certifie que: ");
    doc.text(
      certificate.citizenName || "",
      contentX + certifieLabelWidth + 4, // Added 4mm spacing
      currentY
    );
    currentY += 8;

    doc.setFont("helvetica", "bold");
    doc.text("Demeure à: ", contentX, currentY);
    doc.setFont("helvetica", "normal");
    const demeureLabelWidth = doc.getTextWidth("Demeure à: ");
    doc.text(
      certificate.citizen.adressePrecise || "",
      contentX + demeureLabelWidth + 4, // Added 4mm spacing
      currentY
    );
    currentY += 8;

    doc.text(
      `en résidence dans la Commune de ${certificate.quartier?.commune || ""}`,
      contentX,
      currentY
    );
  }

  private static addDetailedInformation(
    doc: jsPDF,
    certificate: Certificate,
    yPosition: number
  ) {
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const contentX = margin + DESIGN_V2.CONTENT_PADDING;
    const pageHeight = doc.internal.pageSize.height;
    const maxY = pageHeight - margin - DESIGN_V2.FOOTER_HEIGHT - 20;
    let currentY = yPosition;

    console.log("=== Adding Detailed Information ===");
    console.log(`Starting Y position: ${currentY}`);
    console.log(`Max Y allowed: ${maxY}`);
    console.log(`Content X: ${contentX}`);

    // Add spacing after main content
    currentY += 8;

    // Professional horizontal layout for citizen information
    this.addCitizenInformationHorizontal(
      doc,
      certificate,
      currentY,
      contentX,
      maxY
    );
  }

  private static addCitizenInformationHorizontal(
    doc: jsPDF,
    certificate: Certificate,
    startY: number,
    contentX: number,
    maxY: number
  ) {
    let currentY = startY;
    const pageWidth = doc.internal.pageSize.width;
    const rightMargin =
      DESIGN_V2.PAGE_MARGIN +
      DESIGN_V2.BORDER_WIDTH +
      DESIGN_V2.CONTENT_PADDING;
    const availableWidth = pageWidth - contentX - rightMargin;

    console.log(
      "=== Rendering Citizen Information Horizontally (Optimized) ==="
    );
    console.log(`Available width: ${availableWidth}mm`);

    // Set consistent styling
    doc.setFontSize(10);
    doc.setTextColor(COLORS_V2.BLACK);

    // Row 1: Profession + Nationalité (two-column layout)
    const col1Width = availableWidth * 0.6; // 60% for profession
    const col2X = contentX + col1Width + 10; // 10mm gap between columns

    if (certificate.citizen?.profession) {
      doc.setFont("helvetica", "bold");
      doc.text("Profession: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const professionLabelWidth = doc.getTextWidth("Profession: ");
      doc.text(
        certificate.citizen.profession,
        contentX + professionLabelWidth + 2,
        currentY
      );
    }

    // Nationalité on same line
    doc.setFont("helvetica", "bold");
    doc.text("de Nationalité: ", col2X, currentY);
    doc.setFont("helvetica", "normal");
    const nationalityLabelWidth = doc.getTextWidth("de Nationalité: ");
    doc.text("Guinéenne", col2X + nationalityLabelWidth + 2, currentY);

    console.log(`Row 1: Profession + Nationality rendered`);
    currentY += 7;

    // Row 2: Date et lieu de naissance (horizontal)
    if (
      certificate.citizen?.dateNaissance &&
      certificate.citizen?.lieuNaissance
    ) {
      doc.setFont("helvetica", "bold");
      doc.text("Né(e) le: ", contentX, currentY);
      doc.setFont("helvetica", "normal");

      const dateLabelWidth = doc.getTextWidth("Né(e) le: ");
      const dateValue = new Date(
        certificate.citizen.dateNaissance
      ).toLocaleDateString("fr-FR");
      doc.text(dateValue, contentX + dateLabelWidth + 2, currentY);

      // Add "à" and place of birth on same line
      const dateValueWidth = doc.getTextWidth(dateValue);
      const atX = contentX + dateLabelWidth + dateValueWidth + 8;

      doc.setFont("helvetica", "bold");
      doc.text(" à: ", atX, currentY);
      doc.setFont("helvetica", "normal");
      const atWidth = doc.getTextWidth(" à: ");
      doc.text(certificate.citizen.lieuNaissance, atX + atWidth + 2, currentY);

      console.log(
        `Row 2: Birth info rendered: ${dateValue} à ${certificate.citizen.lieuNaissance}`
      );
      currentY += 7;
    }

    // Row 3: Parents (horizontal)
    if (certificate.citizen?.nomPere && certificate.citizen?.nomMere) {
      doc.setFont("helvetica", "bold");
      doc.text("Fils/Fille de: ", contentX, currentY);
      doc.setFont("helvetica", "normal");

      const parentLabelWidth = doc.getTextWidth("Fils/Fille de: ");
      doc.text(
        certificate.citizen.nomPere,
        contentX + parentLabelWidth + 2,
        currentY
      );

      // Add "et de" and mother's name on same line
      const fatherNameWidth = doc.getTextWidth(certificate.citizen.nomPere);
      const etDeX = contentX + parentLabelWidth + fatherNameWidth + 8;

      doc.setFont("helvetica", "bold");
      doc.text(" et de: ", etDeX, currentY);
      doc.setFont("helvetica", "normal");
      const etDeWidth = doc.getTextWidth(" et de: ");
      doc.text(certificate.citizen.nomMere, etDeX + etDeWidth + 2, currentY);

      console.log(
        `Row 3: Parents rendered: ${certificate.citizen.nomPere} et de ${certificate.citizen.nomMere}`
      );
      currentY += 7;
    }

    // Row 4: Date d'installation + Carte électorale (two-column layout)
    if (certificate.citizen?.dateInstallation) {
      doc.setFont("helvetica", "bold");
      doc.text("Réside ce quartier depuis: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const residenceLabelWidth = doc.getTextWidth(
        "Réside ce quartier depuis: "
      );
      const installationDate = new Date(
        certificate.citizen.dateInstallation
      ).toLocaleDateString("fr-FR");
      doc.text(installationDate, contentX + residenceLabelWidth + 2, currentY);
    }

    // Carte électorale on same line (right column)
    if (certificate.citizen?.carteElecteur) {
      doc.setFont("helvetica", "bold");
      doc.text("N° Carte Électorale: ", col2X, currentY);
      doc.setFont("helvetica", "normal");
      const cardLabelWidth = doc.getTextWidth("N° Carte Électorale: ");
      doc.text(
        certificate.citizen.carteElecteur,
        col2X + cardLabelWidth + 2,
        currentY
      );
    }

    console.log(`Row 4: Installation date + Electoral card rendered`);
    currentY += 7;

    // Row 5: Motif
    if (certificate.motif) {
      doc.setFont("helvetica", "bold");
      doc.text("Pour motif: ", contentX, currentY);
      doc.setFont("helvetica", "normal");
      const motifLabelWidth = doc.getTextWidth("Pour motif: ");
      doc.text(certificate.motif, contentX + motifLabelWidth + 2, currentY);
      console.log(`Row 5: Motif rendered: ${certificate.motif}`);
      currentY += 10;
    }

    // Validité
    if (currentY < maxY - 15) {
      doc.setFont("helvetica", "bold");
      doc.setTextColor(COLORS_V2.DARK_GREEN);
      doc.text("Validité: 03 Mois", contentX, currentY);
      console.log("Validity rendered");
      currentY += 10;
    }

    // Formule de clôture
    if (currentY < maxY - 20) {
      doc.setFont("helvetica", "italic");
      doc.setTextColor(COLORS_V2.BLACK);
      doc.text(
        "En foi de quoi le présent certificat est délivré pour servir et valoir ce que de droit.",
        contentX,
        currentY
      );
      console.log("Closing formula rendered");
    }

    console.log(`Final Y position: ${currentY}`);
  }

  private static async addQRCode(doc: jsPDF, securityData: any) {
    try {
      // Génération du QR code
      const qrCodeBase64 = await QRCodeGenerator.generateCertificateQR(
        securityData.verificationUrl,
        securityData.verificationHash
      );

      // Position du QR code (coin inférieur gauche) - aligned with bottom elements
      const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
      const qrX = margin + DESIGN_V2.QR_MARGIN;
      const pageHeight = doc.internal.pageSize.height;
      const bottomAlignY = pageHeight - margin - 15; // Bottom alignment reference
      const qrY = bottomAlignY - DESIGN_V2.QR_SIZE; // QR code bottom-aligned

      // Ajout du QR code
      doc.addImage(
        `data:image/png;base64,${qrCodeBase64}`,
        "PNG",
        qrX,
        qrY,
        DESIGN_V2.QR_SIZE,
        DESIGN_V2.QR_SIZE
      );

      // Texte explicatif sous le QR code
      doc.setFontSize(8);
      doc.setFont("helvetica", "normal");
      doc.setTextColor(COLORS_V2.DARK_GRAY);
      doc.text(
        "Scanner pour vérifier",
        qrX + DESIGN_V2.QR_SIZE / 2,
        qrY + DESIGN_V2.QR_SIZE + 5,
        { align: "center" }
      );
    } catch (error) {
      console.error("Erreur lors de l'ajout du QR code:", error);
      // Continuer sans QR code en cas d'erreur
    }
  }

  private static addModernFooter(doc: jsPDF, certificate: Certificate) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;
    const footerY = pageHeight - margin - DESIGN_V2.FOOTER_HEIGHT;

    // Date et lieu - positioned OUTSIDE and ABOVE signature frame
    const dateDelivrance = certificate.deliveredAt
      ? new Date(certificate.deliveredAt).toLocaleDateString("fr-FR")
      : new Date().toLocaleDateString("fr-FR");

    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.setTextColor(COLORS_V2.BLACK);

    // Position date/location text above signature area - moved higher for better positioning
    const dateY = footerY - 22; // Moved up by 7mm for more professional positioning
    doc.text(
      `Fait à ${certificate.quartier?.commune || ""}, le ${dateDelivrance}`,
      pageWidth - margin - DESIGN_V2.CONTENT_PADDING,
      dateY,
      { align: "right" }
    );

    // Logo Simandou repositioned for bottom alignment with other elements
    if (this.simandouBase64) {
      const logoBottomY = pageHeight - margin - 15; // Bottom-aligned with other elements
      doc.addImage(
        `data:image/png;base64,${this.simandouBase64}`,
        "PNG",
        margin + 37, // Left side positioning
        logoBottomY - 20, // Position so bottom aligns with other elements
        40,
        20
      );
    }
  }

  private static async addModernSignature(
    doc: jsPDF,
    signatureBuffer: ArrayBuffer,
    certificate: Certificate
  ) {
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = DESIGN_V2.PAGE_MARGIN + DESIGN_V2.BORDER_WIDTH;

    // Encadré pour la signature - aligned with tricolor border, slightly reduced for content optimization
    const signatureBoxWidth = 95; // Reduced by 5mm to optimize space
    const signatureBoxHeight = 42; // Reduced by 3mm to maintain alignment
    // Align right edge with tricolor border right edge
    const signatureBoxX =
      pageWidth - margin - DESIGN_V2.BORDER_WIDTH - signatureBoxWidth;

    // Bottom-align signature frame with other elements
    const bottomAlignY = pageHeight - margin - 15;
    const alignedSignatureY = bottomAlignY - signatureBoxHeight;

    doc.setDrawColor(COLORS_V2.GREEN);
    doc.setLineWidth(1);
    doc.rect(
      signatureBoxX,
      alignedSignatureY,
      signatureBoxWidth,
      signatureBoxHeight
    );

    // Titre de la signature - optimized to fit within frame
    doc.setFontSize(7); // Reduced font size for better fit
    doc.setFont("helvetica", "bold");
    doc.setTextColor(COLORS_V2.DARK_GREEN);
    doc.text(
      "Signature et Cachet du",
      signatureBoxX + signatureBoxWidth / 2,
      alignedSignatureY - 2, // Adjusted positioning
      { align: "center" }
    );
    doc.text(
      "Président du Conseil de Quartier",
      signatureBoxX + signatureBoxWidth / 2,
      alignedSignatureY + 4, // Adjusted positioning
      { align: "center" }
    );

    // Image de la signature - optimized positioning within smaller frame
    const base64 = Buffer.from(signatureBuffer).toString("base64");
    doc.addImage(
      `data:image/png;base64,${base64}`,
      "PNG",
      signatureBoxX + 8, // Adjusted margin for smaller frame
      alignedSignatureY + 9, // Adjusted positioning
      signatureBoxWidth - 16, // Adjusted width for smaller frame
      20 // Optimized signature image height for smaller frame
    );

    // Nom du signataire - positioned neatly at bottom
    doc.setFontSize(7); // Reduced font size for better fit
    doc.setFont("helvetica", "normal");
    doc.text(
      certificate.chefQuartierName || "",
      signatureBoxX + signatureBoxWidth / 2,
      alignedSignatureY + signatureBoxHeight - 2, // Adjusted positioning
      { align: "center" }
    );
  }
}
