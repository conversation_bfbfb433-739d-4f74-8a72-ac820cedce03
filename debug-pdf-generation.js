// Debug script to test PDF generation and verify data flow
const fs = require('fs');

// Mock certificate data that should include all required fields
const mockCertificateData = {
  certificate: {
    $id: "test-cert-id",
    reference: "NCR-CON-MATAM-20241201-12345",
    citizenId: "test-citizen-id",
    chefId: "test-chef-id",
    status: "APPROVED",
    motif: "Demande d'emploi",
    createdAt: "2024-01-10T10:00:00.000Z",
    deliveredAt: "2024-01-15T14:30:00.000Z",
    signatureFileId: null,
    citizen: {
      $id: "citizen-id",
      userId: "user-id",
      nom: "<PERSON>allo",
      prenom: "Mamadou",
      dateNaissance: "1990-05-15T00:00:00.000Z",
      lieuNaissance: "Conakry",
      nomPere: "<PERSON><PERSON>",
      nomMere: "<PERSON>oumata Camara",
      nationalite: "<PERSON><PERSON><PERSON><PERSON>",
      profession: "Ingénieur",
      telephone: "+224 123 456 789",
      email: "<EMAIL>",
      carteElecteur: "CE123456789",
      adressePrecise: "Quartier <PERSON>, Rue KA-001",
      dateInstallation: "2020-01-01T00:00:00.000Z",
      numeroBatiment: "B-15",
      proprietaireBatiment: "Propriétaire",
      quartier: "Matam",
      numeroIdentificationUnique: "19900515-20240110-A1B2C3D4",
      status: "APPROVED",
      role: "citizen",
    },
    citizenName: "Mamadou Diallo",
    chefQuartier: {
      $id: "chef-id",
      nom: "Touré",
      prenom: "Alpha",
    },
    chefQuartierName: "Alpha Touré",
    quartier: {
      $id: "quartier-id",
      nom: "Matam",
      commune: "Matam",
      region: "Conakry",
      chefId: "test-chef-id",
    },
  }
};

console.log("=== Debug Certificate Data Structure ===");
console.log("Certificate Reference:", mockCertificateData.certificate.reference);
console.log("Citizen Name:", mockCertificateData.certificate.citizenName);
console.log("Chef Quartier Name:", mockCertificateData.certificate.chefQuartierName);

console.log("\n=== Quartier Information ===");
console.log("Quartier Nom:", mockCertificateData.certificate.quartier?.nom);
console.log("Quartier Commune:", mockCertificateData.certificate.quartier?.commune);
console.log("Quartier Region:", mockCertificateData.certificate.quartier?.region);

console.log("\n=== Citizen Information ===");
console.log("Profession:", mockCertificateData.certificate.citizen?.profession);
console.log("Date Naissance:", mockCertificateData.certificate.citizen?.dateNaissance);
console.log("Lieu Naissance:", mockCertificateData.certificate.citizen?.lieuNaissance);
console.log("Nom Pere:", mockCertificateData.certificate.citizen?.nomPere);
console.log("Nom Mere:", mockCertificateData.certificate.citizen?.nomMere);
console.log("Adresse Precise:", mockCertificateData.certificate.citizen?.adressePrecise);
console.log("Date Installation:", mockCertificateData.certificate.citizen?.dateInstallation);
console.log("Carte Electeur:", mockCertificateData.certificate.citizen?.carteElecteur);
console.log("Numero ID Unique:", mockCertificateData.certificate.citizen?.numeroIdentificationUnique);

console.log("\n=== Certificate Motif ===");
console.log("Motif:", mockCertificateData.certificate.motif);

console.log("\n=== Expected New Fields in PDF ===");
console.log("1. Header Information:");
console.log("   - Région de:", mockCertificateData.certificate.quartier?.region || "MISSING");
console.log("   - Préfecture de:", mockCertificateData.certificate.quartier?.commune || "MISSING");
console.log("   - District/Quartier:", mockCertificateData.certificate.quartier?.nom || "MISSING");

console.log("\n2. Authority Information:");
console.log("   - Je soussigné M./Mme.:", mockCertificateData.certificate.chefQuartierName || "MISSING");
console.log("   - Président du conseil de quartier de:", mockCertificateData.certificate.quartier?.nom || "MISSING");

console.log("\n3. Enhanced Citizen Information:");
console.log("   - All citizen fields should be displayed with bold labels and proper spacing");

console.log("\n=== Data Validation ===");
const requiredFields = [
  { name: "quartier.region", value: mockCertificateData.certificate.quartier?.region },
  { name: "quartier.commune", value: mockCertificateData.certificate.quartier?.commune },
  { name: "quartier.nom", value: mockCertificateData.certificate.quartier?.nom },
  { name: "chefQuartierName", value: mockCertificateData.certificate.chefQuartierName },
  { name: "citizen.profession", value: mockCertificateData.certificate.citizen?.profession },
  { name: "citizen.dateNaissance", value: mockCertificateData.certificate.citizen?.dateNaissance },
  { name: "citizen.lieuNaissance", value: mockCertificateData.certificate.citizen?.lieuNaissance },
  { name: "citizen.nomPere", value: mockCertificateData.certificate.citizen?.nomPere },
  { name: "citizen.nomMere", value: mockCertificateData.certificate.citizen?.nomMere },
  { name: "citizen.adressePrecise", value: mockCertificateData.certificate.citizen?.adressePrecise },
  { name: "citizen.dateInstallation", value: mockCertificateData.certificate.citizen?.dateInstallation },
  { name: "citizen.carteElecteur", value: mockCertificateData.certificate.citizen?.carteElecteur },
  { name: "motif", value: mockCertificateData.certificate.motif },
];

let missingFields = [];
let presentFields = [];

requiredFields.forEach(field => {
  if (!field.value || field.value === "") {
    missingFields.push(field.name);
  } else {
    presentFields.push(field.name);
  }
});

console.log("\n=== Field Availability Report ===");
console.log("✅ Present Fields:", presentFields.length);
presentFields.forEach(field => console.log(`   - ${field}`));

if (missingFields.length > 0) {
  console.log("\n❌ Missing Fields:", missingFields.length);
  missingFields.forEach(field => console.log(`   - ${field}`));
} else {
  console.log("\n✅ All required fields are present!");
}

console.log("\n=== Conclusion ===");
if (missingFields.length === 0) {
  console.log("✅ Data structure is complete. The issue might be in:");
  console.log("   1. PDF rendering logic");
  console.log("   2. Field positioning causing overflow");
  console.log("   3. Font/color settings making text invisible");
  console.log("   4. Data not being passed correctly to PDF generator");
} else {
  console.log("❌ Missing data fields detected. Check data fetching logic.");
}

// Save this data structure for reference
fs.writeFileSync('debug-certificate-data.json', JSON.stringify(mockCertificateData, null, 2));
console.log("\n📄 Certificate data saved to debug-certificate-data.json");
