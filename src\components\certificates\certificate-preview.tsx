"use client";

import { Certificate } from "@/actions/types";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useCertificatePreview } from "@/hooks/use-certificate-preview";
import { AlertCircle, FileText, Loader2, RefreshCw } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";

interface CertificatePreviewProps {
  certificate: Certificate;
  isDelivered?: boolean;
  className?: string;
}

export function CertificatePreview({
  certificate,
  isDelivered = false,
  className = "",
}: CertificatePreviewProps) {
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);

  const {
    generatePreview,
    cleanupPreview,
    clearCache,
    cancelGeneration,
    isGenerating,
    error: previewError,
    clearError,
  } = useCertificatePreview();

  // Mémoriser les propriétés du certificat pour éviter les re-renders
  const certificateKey = useMemo(
    () => `${certificate.$id}-${certificate.status}-${certificate.updatedAt}`,
    [certificate.$id, certificate.status, certificate.updatedAt]
  );

  // Fonction pour charger la prévisualisation avec retry logic
  const loadPreview = useCallback(
    async (forceRefresh = false) => {
      if (!certificate.$id) {
        console.warn("No certificate ID provided");
        return;
      }

      try {
        clearError();
        setLastError(null);

        console.log(
          `Loading preview for certificate ${certificate.$id}, attempt ${
            retryCount + 1
          }`
        );

        const url = await generatePreview(
          certificate.$id,
          certificate.status,
          forceRefresh
        );

        if (url) {
          // Nettoyer l'ancienne URL si elle existe
          if (previewUrl && previewUrl !== url) {
            cleanupPreview(previewUrl);
          }
          setPreviewUrl(url);
          setRetryCount(0); // Reset retry count on success
          console.log("Preview loaded successfully");
        } else if (!isGenerating) {
          // Seulement si ce n'est pas en cours de génération
          throw new Error("Aucune URL de prévisualisation retournée");
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Erreur inconnue";
        console.error("Error loading preview:", errorMessage);
        setLastError(errorMessage);

        // Auto-retry logic (max 2 retries)
        if (retryCount < 2 && !errorMessage.includes("Timeout")) {
          console.log(
            `Auto-retrying preview generation (attempt ${retryCount + 2})`
          );
          setRetryCount((prev) => prev + 1);
          setTimeout(() => loadPreview(true), 2000 * (retryCount + 1)); // Exponential backoff
        } else {
          toast({
            title: "Erreur de prévisualisation",
            description: errorMessage,
            variant: "destructive",
          });
        }
      }
    },
    [
      certificate.$id,
      certificate.status,
      retryCount,
      generatePreview,
      cleanupPreview,
      clearError,
      isGenerating,
      previewUrl,
      toast,
    ]
  );

  // Charger la prévisualisation quand le certificat change
  useEffect(() => {
    let mounted = true;

    const initPreview = async () => {
      if (mounted) {
        await loadPreview();
      }
    };

    initPreview();

    return () => {
      mounted = false;
      cancelGeneration();
    };
  }, [certificateKey]); // Utiliser certificateKey au lieu des propriétés individuelles

  // Nettoyer au démontage
  useEffect(() => {
    return () => {
      if (previewUrl) {
        cleanupPreview(previewUrl);
      }
      cancelGeneration();
    };
  }, [previewUrl, cleanupPreview, cancelGeneration]);

  // Fonction pour forcer le rechargement
  const handleRefresh = useCallback(() => {
    setRetryCount(0);
    clearCache(certificate.$id);
    loadPreview(true);
  }, [certificate.$id, clearCache, loadPreview]);

  // Déterminer l'état d'affichage
  const displayError = previewError || lastError;
  const showLoading = isGenerating && !previewUrl;
  const showError = displayError && !isGenerating && !previewUrl;
  const showPreview = previewUrl && !isGenerating;

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {isDelivered
            ? "Certificat délivré"
            : "Prévisualisation du certificat"}
        </CardTitle>

        <div className="flex items-center gap-2">
          {showLoading && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>
                {retryCount > 0
                  ? `Tentative ${retryCount + 1}/3...`
                  : "Chargement..."}
              </span>
            </div>
          )}

          {(showError || showPreview) && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isGenerating}
              className="flex items-center gap-1"
            >
              <RefreshCw
                className={`h-4 w-4 ${isGenerating ? "animate-spin" : ""}`}
              />
              Actualiser
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="min-h-[700px] bg-muted/30 rounded-lg p-0">
        {showPreview && (
          <iframe
            src={previewUrl}
            className="w-full h-[700px] rounded-lg"
            style={{
              border: "none",
              backgroundColor: "white",
            }}
            title="Prévisualisation du certificat"
            loading="lazy"
          />
        )}

        {showLoading && (
          <div className="flex flex-col items-center justify-center h-[700px] gap-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <div className="text-center space-y-2">
              <p className="text-lg font-medium">
                {retryCount > 0
                  ? `Nouvelle tentative (${retryCount + 1}/3)`
                  : "Génération de la prévisualisation..."}
              </p>
              <p className="text-sm text-muted-foreground">
                Cela peut prendre quelques secondes
              </p>
            </div>
          </div>
        )}

        {showError && (
          <div className="flex flex-col items-center justify-center h-[700px] gap-4">
            <AlertCircle className="h-12 w-12 text-destructive" />
            <div className="text-center space-y-2 max-w-md">
              <p className="text-lg font-medium text-destructive">
                Erreur de prévisualisation
              </p>
              <p className="text-sm text-muted-foreground">{displayError}</p>
              <Button
                onClick={handleRefresh}
                variant="outline"
                className="mt-4"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Réessayer
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
