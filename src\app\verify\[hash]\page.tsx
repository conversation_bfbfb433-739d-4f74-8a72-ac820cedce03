import {
  VerificationResult,
  verifyCertificate,
} from "@/actions/certificate-verification";
import { EnhancedVerificationDisplay } from "@/components/verification/enhanced-verification-display";
import { EnhancedVerificationFooter } from "@/components/verification/enhanced-verification-footer";
import { EnhancedVerificationHeader } from "@/components/verification/enhanced-verification-header";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface VerifyPageProps {
  params: Promise<{
    hash: string;
  }>;
  searchParams: Promise<{
    debug?: string;
    cert?: string;
    ts?: string;
  }>;
}

export async function generateMetadata({
  params,
}: VerifyPageProps): Promise<Metadata> {
  const { hash } = await params;

  return {
    title: `Vérification de Certificat - ${hash.substring(0, 8)}...`,
    description:
      "Vérification d'authenticité d'un certificat de résidence de la République de Guinée",
    robots: "noindex, nofollow", // Éviter l'indexation des pages de vérification
  };
}

export default async function VerifyPage({
  params,
  searchParams,
}: VerifyPageProps) {
  const { hash } = await params;
  const { debug, cert, ts } = await searchParams;

  // Validation basique du format du hash
  if (!hash || hash.length !== 32 || !/^[a-f0-9]+$/i.test(hash)) {
    notFound();
  }

  // Vérification du certificat
  let verificationResult: VerificationResult;

  try {
    verificationResult = await verifyCertificate(hash);
  } catch (error) {
    console.error("Erreur lors de la vérification:", error);
    verificationResult = {
      isValid: false,
      status: "not_found",
      message: "Erreur lors de la vérification du certificat",
    };
  }

  // Informations de debug (si activées)
  const debugInfo =
    debug === "true"
      ? {
          hash,
          certificateId: cert,
          timestamp: ts,
          verificationTime: new Date().toISOString(),
        }
      : undefined;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-red-50">
      {/* Enhanced Header */}
      <EnhancedVerificationHeader />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <EnhancedVerificationDisplay
            result={verificationResult}
            hash={hash}
            debugInfo={debugInfo}
          />
        </div>
      </div>

      {/* Enhanced Footer */}
      <EnhancedVerificationFooter />
    </div>
  );
}
